# Crosshair Feature Documentation

## Overview

The minimap viewer now includes a crosshair functionality that allows users to place visual markers at specific coordinates on the minimap. This feature enhances navigation and position marking capabilities.

## Features

### ✅ Crosshair Placement
- **Right-click** anywhere on the minimap to place crosshairs at that position
- Crosshairs consist of:
  - A **horizontal line** extending from the left edge to the right edge of the minimap
  - A **vertical line** extending from the top edge to the bottom edge of the minimap
  - A **center square outline** (1x1 pixel) marking the exact selected position
  - All elements intersect at the clicked position

### ✅ Visual Design
- **Lines**: Super thin (0.25px) BLACK lines that extend across the entire minimap
- **Center Square**: 1x1 pixel square with very thin (0.1px) BLACK outline
- **Color**: Black with slight transparency for lines, fully opaque for center square
- **Style**: Minimal visual interference while maintaining precision
- **Layering**: Lines at Z-index 1000, center square at Z-index 1001 (on top)
- **No Fill**: Center square is outline-only, doesn't cover the selected pixel

### ✅ Global Persistence Across Floors
- **One global crosshair** that appears on ALL floors at the same position
- Crosshairs automatically appear when switching to any floor
- No need to recreate crosshairs - they persist globally
- Same crosshair position visible across all floors simultaneously

### ✅ Interactive Behavior
- **Placement**: Right-click to place global crosshairs (appears on all floors)
- **Pixel Snapping**: Crosshairs automatically snap to exact pixel centers for game coordinate alignment
- **Replacement**: Right-click a new position to move crosshairs globally
- **Clearing**: Press `Ctrl+C` to clear global crosshairs from all floors
- **Zoom/Pan**: Crosshairs remain fixed to their scene coordinates during zoom and pan operations

## Usage Instructions

### Basic Usage
1. **Place Global Crosshairs**: Right-click anywhere on the minimap
2. **Pixel-Perfect Placement**: Crosshairs automatically snap to exact pixel centers
3. **Move Crosshairs**: Right-click a new position (moves crosshairs globally on all floors)
4. **Clear Crosshairs**: Press `Ctrl+C` to remove crosshairs from all floors

### Multi-Floor Usage
1. Place crosshairs on any floor by right-clicking
2. Switch to any other floor using floor controls
3. **Crosshairs automatically appear** at the same position on the new floor
4. Switch between any floors - crosshairs persist globally
5. All floors show the same crosshair position simultaneously

## Technical Implementation

### Key Components

#### MinimapGraphicsView Class
- `crosshair_horizontal`: QGraphicsLineItem for horizontal line
- `crosshair_vertical`: QGraphicsLineItem for vertical line
- `crosshair_position`: QPointF storing current crosshair scene coordinates
- `floor_crosshair_positions`: Dict mapping floor IDs to crosshair positions

#### Core Methods
- `place_crosshairs(scene_pos)`: Creates crosshairs at specified position
- `remove_crosshairs()`: Removes current crosshairs from scene
- `restore_crosshairs(floor_id)`: Restores saved crosshairs for a floor
- `save_crosshair_position(floor_id)`: Saves current crosshair position
- `clear_all_crosshairs()`: Clears crosshairs from all floors

### Event Handling
- **Mouse Events**: Right-click detection in `mousePressEvent()`
- **Keyboard Events**: `Ctrl+C` shortcut in `keyPressEvent()`
- **Floor Changes**: Automatic save/restore in `set_floor()` method

### Coordinate System
- Uses **scene coordinates** for position storage
- Crosshairs span the entire scene rectangle
- Positions are preserved across zoom/pan operations
- Coordinates are logged for debugging purposes

## Testing

### Test Script
Run `test_crosshairs.py` to test the functionality:
```bash
python test_crosshairs.py
```

### Manual Testing Checklist
- [ ] Right-click places crosshairs
- [ ] Crosshairs appear as red lines spanning full minimap
- [ ] Right-click new position moves crosshairs
- [ ] Ctrl+C clears crosshairs
- [ ] Crosshairs persist when switching floors
- [ ] Each floor has independent crosshair position
- [ ] Crosshairs remain fixed during zoom/pan
- [ ] Crosshairs are drawn on top of minimap content

## Logging

The feature includes comprehensive logging:
- `INFO`: Crosshair placement and restoration events
- `DEBUG`: Crosshair position saving events
- Coordinates are logged with 2 decimal precision
- Floor-specific operations are clearly identified

### ✅ Enhanced Zoom Capabilities
- **Maximum Zoom**: Increased from 5.0x to 20.0x for pixel-precise selection
- **Zoom Range**: 0.1x to 20.0x (slider range: 10 to 2000)
- **Pixel Precision**: Crosshairs can be placed with sub-pixel accuracy
- **High-Resolution Support**: Works well with detailed minimap images

## Recent Improvements (v1.1)

### Fixed Issues
- ✅ **Global Crosshairs**: Implemented single crosshair that appears on ALL floors
- ✅ **Pixel Snapping**: Crosshairs now snap to exact pixel centers for game coordinate alignment
- ✅ **Automatic Persistence**: Crosshairs automatically appear when switching floors (no recreation needed)
- ✅ **Black Lines**: Changed to black (0.25px) lines for better contrast and minimal interference
- ✅ **Ultra-Thin Center**: Very thin (0.1px) black square outline that barely covers the pixel
- ✅ **Enhanced Zoom**: Increased maximum zoom from 5.0x to 20.0x for detailed inspection
- ✅ **Visual Precision**: Crosshairs provide clear pixel marking without obscuring content

## Future Enhancements

Potential improvements for future versions:
- Multiple crosshairs per floor
- Different crosshair colors/styles
- Crosshair labels or annotations
- Export crosshair positions to file
- Import crosshair positions from file
- Crosshair opacity adjustment
- Custom crosshair shapes (circle, square, etc.)
- Coordinate display tooltip
- Crosshair snapping to grid

## Compatibility

- **PyQt6**: Fully compatible with PyQt6 graphics framework
- **Camera System**: Integrates seamlessly with existing camera position preservation
- **Floor System**: Works with all floor navigation features
- **Zoom/Pan**: Compatible with all existing zoom and pan operations
